import React, { useState, useEffect } from 'react';
import { MaintenanceIcon } from './components/MaintenanceIcon';
import { CrimsonLogo } from './components/CrimsonLogo';

const App: React.FC = () => {
  const [timeLeft, setTimeLeft] = useState('');
  const [maintenanceOver, setMaintenanceOver] = useState(false);
  const [maintenanceDate, setMaintenanceDate] = useState('');
  const [maintenanceTime, setMaintenanceTime] = useState('');

  useEffect(() => {
    // Set maintenance window for September 12, 2025 from 5 PM to 10 PM local time
    const getMaintenanceTimes = () => {
      const startTime = new Date(2025, 8, 12, 17, 0, 0); // September is month 8 (0-indexed)
      const endTime = new Date(2025, 8, 12, 22, 0, 0);
      return { startTime, endTime };
    };
    
    const { startTime, endTime } = getMaintenanceTimes();

    const options: Intl.DateTimeFormatOptions = { weekday: 'long', month: 'long', day: 'numeric' };
    setMaintenanceDate(startTime.toLocaleDateString(undefined, options));
    
    const timeFormatter = new Intl.DateTimeFormat('en-US', { hour: 'numeric', minute: 'numeric', hour12: true, timeZoneName: 'short' });
    setMaintenanceTime(`${timeFormatter.format(startTime)} - ${timeFormatter.format(endTime)}`);

    const calculateTimeLeft = () => {
        const currentTime = new Date();
        const { endTime } = getMaintenanceTimes();
        const difference = endTime.getTime() - currentTime.getTime();
        
        if (difference > 0) {
            const hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
            const minutes = Math.floor((difference / 1000 / 60) % 60);
            const seconds = Math.floor((difference / 1000) % 60);

            setTimeLeft(
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
            );
            setMaintenanceOver(false);
        } else {
            setTimeLeft('00:00:00');
            setMaintenanceOver(true);
            if(timer) clearInterval(timer);
        }
    };

    const timer = setInterval(calculateTimeLeft, 1000);
    calculateTimeLeft(); // initial call

    return () => clearInterval(timer);
  }, []);

  return (
    <main className="bg-slate-100 dark:bg-slate-900 min-h-screen flex items-center justify-center p-3 sm:p-4 antialiased">
      <div className="w-full max-w-4xl mx-auto">
        <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl p-6 sm:p-8 lg:p-10 transform hover:scale-[1.01] transition-transform duration-500 ease-in-out text-center">

          {/* Crimson Logo */}
          <div className="mb-6 sm:mb-8">
            <CrimsonLogo className="mx-auto" width={220} height={65} />
          </div>

          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-slate-800 dark:text-white mb-3 sm:mb-4">
            Scheduled Maintenance
          </h1>

          <p className="text-slate-500 dark:text-slate-400 text-base sm:text-lg mb-6 sm:mb-8 max-w-2xl mx-auto text-center leading-relaxed">
            We’re improving our infrastructure to serve you better. We appreciate your patience and expect to be back online shortly.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 text-left text-slate-600 dark:text-slate-300 mb-6 sm:mb-8 max-w-4xl mx-auto">
              <div className="flex items-start p-3 sm:p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 dark:text-red-400 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path></svg>
                  <span className="text-sm sm:text-base"><strong className="font-semibold text-slate-800 dark:text-slate-100">Your data is safe.</strong> All information remains secure during this period.</span>
              </div>

              <div className="flex items-start p-3 sm:p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 dark:text-red-400 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path></svg>
                  <span className="text-sm sm:text-base"><strong className="font-semibold text-slate-800 dark:text-slate-100">No action required.</strong> The system will return to normal once maintenance is complete.</span>
              </div>
          </div>

          <div className="bg-slate-100 dark:bg-slate-700 rounded-xl p-4 sm:p-6 mb-6 sm:mb-8 border border-slate-200 dark:border-slate-600 text-center">
            <h2 className="text-base sm:text-lg font-semibold text-slate-700 dark:text-slate-100 mb-2">Estimated Maintenance Window</h2>
            <p className="text-lg sm:text-xl font-bold text-red-600 dark:text-red-400">
              {maintenanceDate}
            </p>
            <p className="text-sm sm:text-base text-slate-500 dark:text-slate-400">
              {maintenanceTime}
            </p>
            {timeLeft && (
              <div className="mt-3 sm:mt-4">
                 <h3 className="text-xs sm:text-sm font-semibold text-slate-700 dark:text-slate-100 mb-1">
                  {maintenanceOver ? 'Service Update' : 'Estimated Time Remaining'}
                </h3>
                {maintenanceOver ? (
                  <p className="text-lg sm:text-xl font-bold text-green-600 dark:text-green-400">
                    We should be back online shortly!
                  </p>
                ) : (
                  <p className="text-2xl sm:text-3xl font-mono font-bold text-slate-800 dark:text-slate-200 tracking-wider" aria-live="polite">
                    {timeLeft}
                  </p>
                )}
              </div>
            )}
          </div>

          <hr className="my-4 sm:my-6 border-slate-200 dark:border-slate-700" />

          <p className="text-slate-500 dark:text-slate-400 text-xs sm:text-sm">
            If you have any urgent questions, please contact our support team at <a href="mailto:<EMAIL>" className="text-red-500 hover:text-red-600 font-semibold underline"><EMAIL></a>.
          </p>
        </div>

        <footer className="text-center mt-4 sm:mt-6">
          <p className="text-slate-400 dark:text-slate-500 text-xs sm:text-sm">
            &copy; {new Date().getFullYear()} CMDI. All Rights Reserved.
          </p>
        </footer>
      </div>
    </main>
  );
};

export default App;
